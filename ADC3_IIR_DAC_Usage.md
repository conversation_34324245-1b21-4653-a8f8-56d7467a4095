# ADC3 → IIR滤波 → DAC输出 使用说明

## 功能概述
本系统实现了完整的数字信号处理链路：
1. **ADC3采样**: 采集输入信号（4096个采样点）
2. **IIR滤波**: 对采样数据进行实时数字滤波
3. **DAC输出**: 将滤波后的数据通过DAC输出
4. **FFT分析**: 对滤波后的数据进行谐波分析

## 使用步骤

### 第一步：执行扫频测试
1. 点击 **"SWEEP ON"** 按钮
2. 系统自动执行三阶段扫频分析
3. 自动识别滤波器类型并计算IIR参数
4. 扫频完成后显示滤波器系数

### 第二步：启动ADC3采样
1. 点击 **"ADC3 OFF"** 按钮
2. 按钮变为 **"ADC3 ON"**，开始连续采样

### 第三步：启用IIR滤波和DAC输出
1. 点击 **"DAC OUT"** 按钮
2. 按钮变为 **"DAC ON"**，启用完整处理链路
3. 系统状态：ADC3 → IIR滤波 → DAC输出

### 第四步：观察结果
- **串口输出**: 显示滤波前后的数据对比
- **DAC输出**: PA4引脚输出滤波后的模拟信号
- **FFT分析**: 基于滤波后数据的谐波分析结果

## 技术参数

### 采样参数
- **ADC3采样率**: 815.534 kHz
- **采样点数**: 4096点
- **采样精度**: 12位 (0-4095)
- **电压范围**: 0-3.3V

### IIR滤波器
- **滤波器类型**: 二阶数字滤波器
- **结构**: 直接型II双二阶节
- **精度**: 32位浮点运算
- **支持类型**: 低通、高通、带通、带阻

### DAC输出
- **输出频率**: 400 kHz（ADC采样率的一半）
- **输出精度**: 12位 (0-4095)
- **输出引脚**: PA4
- **电压范围**: 0-3.3V

## 串口输出示例

### IIR滤波过程
```
=== ADC3 IIR FILTERING START ===
ADC3 DC component: 2048.00
Original vs Filtered (first 10 samples):
ADC3[0]: 0.123456V -> 0.098765V (ADC: 2100 -> 2070)
ADC3[1]: 0.124567V -> 0.099876V (ADC: 2105 -> 2075)
...
=== ADC3 IIR FILTERING COMPLETE ===
```

### DAC输出状态
```
Starting DAC output of filtered ADC3 data...
DAC output complete. 4096 samples output.
```

### FFT谐波分析
```
=== ADC3 HARMONIC ANALYSIS (IIR Filtered Data) ===
基频: 1000.00 Hz, 幅度: 1.234 V
2次谐波: 2000.00 Hz, 幅度: 0.123 V
3次谐波: 3000.00 Hz, 幅度: 0.045 V
```

## 按钮状态说明

### "DAC OUT" / "DAC ON" / "DAC OFF"
- **DAC OUT**: 初始状态，IIR滤波器未就绪
- **DAC ON**: IIR滤波和DAC输出已启用
- **DAC OFF**: IIR滤波和DAC输出已禁用
- **NO IIR**: IIR滤波器未就绪，需要先执行扫频

### "ADC3 OFF" / "ADC3 ON"
- **ADC3 OFF**: ADC3采样未启动
- **ADC3 ON**: ADC3连续采样已启动

## 信号处理流程

```
输入信号 → ADC3采样 → IIR数字滤波 → DAC输出 → 滤波后的模拟信号
                ↓
            FFT谐波分析 → 串口显示结果
```

## 应用场景

1. **信号滤波**: 去除输入信号中的噪声和干扰
2. **频率选择**: 提取特定频率范围的信号成分
3. **信号调理**: 改善信号质量用于后续处理
4. **实时处理**: 低延迟的数字信号处理

## 注意事项

1. **内存优化**: 系统直接在ADC3缓冲区上进行滤波，节省内存
2. **实时性能**: DAC输出频率为400kHz，确保实时处理
3. **滤波器稳定性**: 系统自动检查IIR系数的稳定性
4. **数据完整性**: 每次处理4096个完整的采样点

## 故障排除

### 问题1: 按钮显示"NO IIR"
**解决**: 先执行扫频测试，等待IIR参数计算完成

### 问题2: DAC无输出
**解决**: 确保ADC3采样已启动，且IIR滤波已启用

### 问题3: 滤波效果不明显
**解决**: 检查输入信号频率是否在滤波器通带内

### 问题4: 系统响应慢
**解决**: 关闭不必要的功能，减少CPU负载
