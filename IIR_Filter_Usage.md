# IIR滤波器使用说明

## 概述
本系统现在支持基于扫频结果自动计算IIR滤波器参数，并在单片机内部构建数字IIR滤波器进行实时信号处理。

## 功能特性

### 1. 自动IIR参数计算
- 根据扫频测试结果自动识别滤波器类型（低通、高通、带通、带阻）
- 使用双线性变换方法计算二阶IIR滤波器系数
- 支持自定义截止频率和Q值计算方法

### 2. 支持的滤波器类型
- **低通滤波器 (Low-Pass)**: 截止频率为第二次扫频中首个电压比小于0.7的点
- **高通滤波器 (High-Pass)**: 截止频率为第二次扫频中首个电压比大于0.7的点  
- **带通滤波器 (Band-Pass)**: 中心频率为最大增益点
- **带阻滤波器 (Band-Stop)**: 中心频率为最小增益点

### 3. 实时滤波处理
- 对ADC1采样数据进行实时IIR滤波
- 支持开关控制实时滤波功能
- 输出滤波前后的对比数据

## 使用步骤

### 步骤1: 执行扫频测试
1. 点击 "SWEEP ON" 按钮启动扫频测试
2. 系统将自动执行三阶段扫频：
   - 低频检测 (1Hz-1kHz)
   - 主扫频 (1kHz-100kHz) 
   - 归一化扫频 (1kHz-400kHz)
3. 扫频完成后系统自动识别滤波器类型并计算IIR参数

### 步骤2: 查看IIR系数
扫频完成后，系统会在串口和LCD上显示：
- 滤波器类型
- IIR系数 (b0, b1, b2, a1, a2)
- 传递函数
- 脉冲响应和阶跃响应测试结果

### 步骤3: 启用实时滤波
1. 点击 "IIR TEST" 按钮（现在变为 "IIR OFF"）
2. 按钮变为 "IIR ON"，表示实时滤波已启用
3. 再次点击可关闭实时滤波

### 步骤4: 测试滤波效果
1. 确保实时滤波已启用 ("IIR ON")
2. 启动ADC1采样
3. 系统会自动对采样数据进行IIR滤波
4. 在串口输出中可以看到原始数据和滤波后数据的对比

## 技术参数

### IIR滤波器规格
- **滤波器结构**: 直接型II双二阶节 (Direct Form II Biquad)
- **滤波器阶数**: 2阶
- **采样频率**: 815.534 kHz
- **数据精度**: 32位浮点
- **系数精度**: 8位小数显示

### 计算方法
- **双线性变换**: s域到z域的转换
- **预扭曲**: 补偿频率响应的非线性失真
- **Q值限制**: 0.1 ≤ Q ≤ 100，异常值自动设为0.707

## 串口输出格式

### IIR系数显示
```
=== IIR FILTER COEFFICIENTS ===
Filter Type: Low-Pass
Numerator coefficients (b):
  b0 = 0.12345678
  b1 = 0.24691356
  b2 = 0.12345678
Denominator coefficients (a):
  a0 = 1.000000 (normalized)
  a1 = -0.12345678
  a2 = 0.06172839
Transfer Function:
H(z) = (0.123457 + 0.246914*z^-1 + 0.123457*z^-2) / (1 + -0.123457*z^-1 + 0.061728*z^-2)
```

### 滤波测试结果
```
=== IIR FILTER TEST ===
Impulse Response (first 10 samples):
h[0] = 0.123457
h[1] = 0.234567
...
Step Response (samples 10-20):
s[10] = 0.987654
s[11] = 0.998765
...
```

### 实时滤波数据
```
=== REAL-TIME IIR FILTERING ===
Original vs Filtered (first 10 samples):
Sample[0]: 1.650000V -> 1.234567V
Sample[1]: 1.651000V -> 1.235678V
...
FILTERED_SAMPLES_START
1.234567    1.235678    1.236789    ...
FILTERED_SAMPLES_END
```

## 注意事项

1. **内存使用**: IIR滤波器使用额外的内存存储滤波后的数据
2. **计算性能**: 实时滤波会增加CPU负载，建议在必要时才启用
3. **参数有效性**: 只有在扫频测试完成后IIR参数才有效
4. **滤波器稳定性**: 系统会自动检查系数的合理性，异常时使用默认值

## 故障排除

### 问题1: 按钮显示 "NO IIR"
**原因**: IIR滤波器未就绪
**解决**: 先执行扫频测试，等待系统计算IIR参数

### 问题2: 滤波效果不明显
**原因**: 可能是Q值过小或滤波器类型识别错误
**解决**: 检查扫频结果，确认滤波器类型识别正确

### 问题3: 系统响应变慢
**原因**: 实时滤波增加了计算负载
**解决**: 在不需要时关闭实时滤波功能

## 扩展功能

未来可以考虑添加：
- 多级联IIR滤波器
- 自适应滤波器参数调整
- 频率响应实时显示
- 滤波器参数手动调整界面
